:root {
  --color-primary: #310bee;
  --color-primary-hover: #2809d4;
  --color-primary-light: #4a1fff;
  --color-secondary: #292249;
  --color-secondary-hover: #3a3168;
  --color-background: #131023;
  --color-background-light: #1d1834;
  --color-text-primary: #ffffff;
  --color-text-secondary: #9a90cb;
  --color-text-muted: #6b6b8a;
  --color-border: #3a3168;
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  --font-primary: "Space Grotesk", "Noto Sans", sans-serif;
  --font-secondary: "Noto Sans", sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  --font-size-5xl: 3rem;
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  --spacing-3xl: 4rem;
  --transition-fast: 0.15s ease-in-out;
  --transition-normal: 0.3s ease-in-out;
  --transition-slow: 0.5s ease-in-out;
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-full: 9999px;
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1200px;
}

*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  line-height: 1.15;
  -webkit-text-size-adjust: 100%;
  scroll-behavior: smooth;
}

body {
  margin: 0;
  font-family: var(--font-primary);
  font-size: var(--font-size-base);
  line-height: 1.6;
  color: var(--color-text-primary);
  background-color: var(--color-background);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeSpeed;
}

ul, ol {
  list-style: none;
}

a {
  text-decoration: none;
  color: inherit;
}

img, picture, video, canvas, svg {
  display: block;
  max-width: 100%;
  height: auto;
}

input, button, textarea, select {
  font: inherit;
  color: inherit;
}

button {
  border: none;
  background: none;
  cursor: pointer;
}

:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

html, body {
  overflow-x: hidden;
  width: 100%;
  max-width: 100vw;
  box-sizing: border-box;
}

*, *::before, *::after {
  box-sizing: border-box;
}

/* Prevent horizontal overflow globally */
.app-container,
.layout-container,
.main-content,
.content-container {
  max-width: 100vw;
  overflow-x: hidden;
}

.app-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.layout-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  flex: 1;
}

.main-content {
  display: flex;
  flex: 1;
  justify-content: center;
  padding: var(--spacing-lg) var(--spacing-xl);
  width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
}

.content-container {
  display: flex;
  flex-direction: column;
  max-width: 1200px;
  flex: 1;
  width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}
.portfolio-header {
  position: sticky;
  top: 0;
  z-index: 1000;
  background-color: rgba(19, 16, 35, 0.95);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--color-border);
  transition: all var(--transition-normal);
  padding: var(--spacing-md) var(--spacing-lg);
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  box-sizing: border-box;
}
.portfolio-header.scrolled {
  background-color: rgba(19, 16, 35, 0.98);
  box-shadow: var(--shadow-lg);
}

.header__brand {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  flex-shrink: 0;
}
.header__logo {
  width: 32px;
  height: 32px;
  color: var(--color-primary);
  transition: transform var(--transition-normal);
}
.header__logo:hover {
  transform: scale(1.1);
}
.header__logo svg {
  width: 100%;
  height: 100%;
}
.header__title {
  font-size: var(--font-size-lg);
  font-weight: 700;
  color: var(--color-text-primary);
  margin: 0;
  white-space: nowrap;
}
@media (max-width: 767px) {
  .header__title {
    font-size: var(--font-size-base);
  }
  
  .header__brand {
    gap: var(--spacing-sm);
  }
  
  .header__logo {
    width: 28px;
    height: 28px;
  }
}
.header__nav-container {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  flex: 1;
  justify-content: flex-end;
}
@media (max-width: 767px) {
  .header__nav-container {
    gap: var(--spacing-md);
  }
}

.portfolio-nav {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}
@media (max-width: 767px) {
  .portfolio-nav {
    display: none;
  }
}
.portfolio-nav__link {
  position: relative;
  padding: var(--spacing-sm) var(--spacing-md);
  color: var(--color-text-secondary);
  text-decoration: none;
  font-weight: 500;
  font-size: var(--font-size-sm);
  transition: all var(--transition-normal);
  border-radius: var(--radius-md);
  white-space: nowrap;
}
.portfolio-nav__link:hover {
  color: var(--color-primary-light);
  background-color: rgba(49, 11, 238, 0.1);
  transform: translateY(-1px);
}
.portfolio-nav__link.active {
  color: var(--color-primary);
  background-color: rgba(49, 11, 238, 0.15);
}
.portfolio-nav__link::after {
  content: "";
  position: absolute;
  bottom: -2px;
  left: 50%;
  width: 0;
  height: 2px;
  background-color: var(--color-primary);
  transition: all var(--transition-normal);
  transform: translateX(-50%);
}
.portfolio-nav__link:hover::after, .portfolio-nav__link.active::after {
  width: 80%;
}
.portfolio-nav__link:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--radius-full);
  font-weight: 600;
  font-size: var(--font-size-sm);
  text-align: center;
  transition: all var(--transition-normal);
  cursor: pointer;
  border: none;
  text-decoration: none;
  position: relative;
  overflow: hidden;
}
.btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--transition-slow);
}
.btn:hover::before {
  left: 100%;
}
.btn--primary {
  background-color: var(--color-primary);
  color: var(--color-text-primary);
}
.btn--primary:hover {
  background-color: var(--color-primary-hover);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}
.btn--primary:active {
  transform: translateY(0);
}
.btn--secondary {
  background-color: var(--color-secondary);
  color: var(--color-text-primary);
}
.btn--secondary:hover {
  background-color: var(--color-secondary-hover);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}
.btn--outline {
  background-color: transparent;
  border: 2px solid var(--color-primary);
  color: var(--color-primary);
}
.btn--outline:hover {
  background-color: var(--color-primary);
  color: var(--color-text-primary);
  transform: translateY(-2px);
}
@media (min-width: 768px) {
  .btn {
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: var(--font-size-base);
  }
}

.hero-section {
  padding: var(--spacing-2xl) 0;
}

.hero {
  position: relative;
  overflow: hidden;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-3xl);
  align-items: center;
  min-height: 500px;
}

@media (max-width: 767px) {
  .hero {
    grid-template-columns: 1fr;
    gap: var(--spacing-xl);
    text-align: center;
    min-height: auto;
    padding: var(--spacing-lg) 0;
  }
  
  .hero__image {
    order: -1;
    min-height: 250px;
    max-height: 300px;
  }
  
  .hero__title {
    font-size: var(--font-size-2xl);
    line-height: 1.3;
  }
  
  .hero__subtitle {
    font-size: var(--font-size-base);
    line-height: 1.5;
  }
  
  .hero__buttons {
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-sm);
  }
  
  .hero__buttons .btn {
    width: 100%;
    max-width: 280px;
  }
}

.hero__content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
  flex: 1;
  opacity: 0;
  animation: fadeIn 0.8s ease-in-out forwards;
  animation-delay: 0.2s;
}

.hero__text {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.hero__image {
  position: relative;
  overflow: hidden;
  border-radius: var(--radius-xl);
  min-height: 400px;
}

.hero__image-bg {
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  border-radius: var(--radius-xl);
}
.hero__image::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(49, 11, 238, 0.1), rgba(154, 144, 203, 0.1));
  opacity: 0;
  transition: opacity var(--transition-normal);
  z-index: 1;
}
.hero__image:hover::before {
  opacity: 1;
}
.hero__image img {
  transition: transform var(--transition-slow);
}
.hero__image:hover img {
  transform: scale(1.05);
}
.hero__title {
  font-size: var(--font-size-4xl);
  font-weight: 900;
  line-height: 1.2;
  color: var(--color-text-primary);
  margin: 0;
  padding: 0;
}

.hero__subtitle {
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
  line-height: 1.6;
  margin: 0;
  padding: 0;
}
.hero__buttons {
  display: flex;
  gap: var(--spacing-md);
  flex-wrap: wrap;
  opacity: 0;
  transform: translateY(30px);
  animation: slideUp 0.8s ease-out forwards;
  animation-delay: 0.8s;
}

/* Section Styles */
.section-title {
  font-size: var(--font-size-3xl);
  font-weight: 700;
  color: var(--color-text-primary);
  margin: var(--spacing-3xl) 0 var(--spacing-xl) 0;
  text-align: center;
  line-height: 1.2;
}

.section-subtitle {
  font-size: var(--font-size-2xl);
  font-weight: 600;
  color: var(--color-text-primary);
  margin: var(--spacing-2xl) 0 var(--spacing-lg) 0;
  text-align: center;
  line-height: 1.3;
}

.section-description {
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
  line-height: 1.6;
  text-align: center;
  max-width: 800px;
  margin: 0 auto var(--spacing-xl) auto;
}

/* Social Media Cards */
.social-media {
  margin: var(--spacing-2xl) 0;
}

.social-media__grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
  margin-top: var(--spacing-lg);
}

.social-media__card {
  background-color: var(--color-background-light);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  text-align: center;
  transition: all var(--transition-normal);
  cursor: pointer;
}

.social-media__card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-xl);
  border-color: var(--color-primary);
}

.social-media__card:hover .social-media__icon {
  transform: scale(1.1);
  color: var(--color-primary-light);
}

.social-media__icon {
  display: flex;
  justify-content: center;
  margin-bottom: var(--spacing-sm);
  color: var(--color-text-secondary);
  transition: all var(--transition-normal);
}

.social-media__label {
  font-weight: 600;
  color: var(--color-text-primary);
  margin: 0;
  font-size: var(--font-size-sm);
}

/* Education Section */
.education-section {
  margin: var(--spacing-2xl) 0;
}

.education__card {
  display: grid;
  grid-template-columns: 100px 1fr;
  gap: var(--spacing-lg);
  background-color: var(--color-background-light);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  transition: all var(--transition-normal);
  margin-bottom: var(--spacing-lg);
}

.education__card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-xl);
  border-color: var(--color-primary);
}

.education__image {
  width: 100px;
  height: 100px;
  border-radius: var(--radius-lg);
  overflow: hidden;
  background-color: var(--color-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
}

.education__image-placeholder {
  width: 60px;
  height: 60px;
  background-color: var(--color-primary);
  border-radius: var(--radius-md);
  opacity: 0.7;
}

.education__content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.education__title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--color-text-primary);
  margin: 0;
}

.education__description {
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  line-height: 1.5;
  margin: 0;
}

.education__degree {
  color: var(--color-text-muted);
  font-size: var(--font-size-xs);
  font-weight: 500;
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Experience Section */
.experience-section {
  margin: var(--spacing-2xl) 0;
}

.experience__card {
  display: grid;
  grid-template-columns: 100px 1fr;
  gap: var(--spacing-lg);
  background-color: var(--color-background-light);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  transition: all var(--transition-normal);
  margin-bottom: var(--spacing-lg);
}

.experience__card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-xl);
  border-color: var(--color-primary);
}

.experience__image {
  width: 100px;
  height: 100px;
  border-radius: var(--radius-lg);
  overflow: hidden;
  background-color: var(--color-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
}

.experience__image-placeholder {
  width: 60px;
  height: 60px;
  background-color: var(--color-primary);
  border-radius: var(--radius-md);
  opacity: 0.7;
}

.experience__content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.experience__title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--color-text-primary);
  margin: 0;
}

.experience__description {
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  line-height: 1.5;
  margin: 0;
}

.experience__position {
  color: var(--color-text-muted);
  font-size: var(--font-size-xs);
  font-weight: 500;
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Social Media Section */
.social-media-section {
  margin: var(--spacing-2xl) 0;
  text-align: center;
}

.social-media__container {
  display: flex;
  justify-content: center;
  gap: var(--spacing-lg);
  flex-wrap: wrap;
}

@media (max-width: 767px) {
  .social-media__container {
    gap: var(--spacing-md);
  }
}

/* Contact Info */
.contact-info {
  text-align: center;
  color: var(--color-text-secondary);
  font-size: var(--font-size-base);
  margin: var(--spacing-xl) 0;
  line-height: 1.6;
}

/* About Section Stats */
.about__stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-lg);
  margin-top: var(--spacing-xl);
  text-align: center;
}

@media (max-width: 767px) {
  .about__stats {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
}

.stat {
  background-color: var(--color-background-light);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  transition: all var(--transition-normal);
}

.stat:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-xl);
  border-color: var(--color-primary);
}

.stat__number {
  font-size: var(--font-size-3xl);
  font-weight: 900;
  color: var(--color-primary);
  margin: 0 0 var(--spacing-sm) 0;
  line-height: 1;
}

.stat__label {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin: 0;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}



/* Responsive Design for Education and Experience */
@media (max-width: 767px) {
  .education__card,
  .experience__card {
    grid-template-columns: 1fr;
    text-align: center;
    gap: var(--spacing-md);
  }
  
  .education__image,
  .experience__image {
    margin: 0 auto;
  }
}

.skills__grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
  margin-top: var(--spacing-lg);
}

@media (max-width: 767px) {
  .skills__grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
  }
  
  /* Minimize animations on mobile for better performance */
  .skills__card {
    transition: transform var(--transition-fast), box-shadow var(--transition-fast);
  }
  
  .skills__card:hover {
    transform: translateY(-2px); /* Reduced transform */
    box-shadow: var(--shadow-md); /* Lighter shadow */
  }
  
  .skills__card::before {
    display: none; /* Remove shimmer effect on mobile */
  }
  
  .skills__card:hover .skills__icon {
    transform: scale(1.05); /* Reduced scale, remove rotation */
    color: var(--color-primary-light);
  }
  
  /* Reduce scroll animation for mobile */
  .animate-on-scroll {
    transition: all 0.3s ease-out; /* Faster animation */
  }
}
.skills__card {
  background-color: var(--color-background-light);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}
.skills__card::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(49, 11, 238, 0.1), transparent);
  transition: left var(--transition-slow);
}
.skills__card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-xl);
  border-color: var(--color-primary);
}
.skills__card:hover::before {
  left: 100%;
}
.skills__card:hover .skills__icon {
  transform: scale(1.1) rotate(5deg);
  color: var(--color-primary-light);
}
.skills__icon {
  transition: all var(--transition-normal);
  margin-bottom: var(--spacing-sm);
}
.skills__title {
  font-weight: 600;
  color: var(--color-text-primary);
}

.projects__container {
  display: flex;
  gap: var(--spacing-lg);
  overflow-x: auto;
  padding: var(--spacing-lg) var(--spacing-md);
  scroll-snap-type: x mandatory;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: thin;
  scrollbar-color: var(--color-primary) var(--color-background-light);
}

.projects__container::-webkit-scrollbar {
  height: 8px;
}

.projects__container::-webkit-scrollbar-track {
  background: var(--color-background-light);
  border-radius: var(--radius-full);
}

.projects__container::-webkit-scrollbar-thumb {
  background: var(--color-primary);
  border-radius: var(--radius-full);
  transition: background var(--transition-normal);
}

.projects__container::-webkit-scrollbar-thumb:hover {
  background: var(--color-primary-light);
}

.projects__card {
  min-width: 400px;
  max-width: 400px;
  height: 480px;
  flex-shrink: 0;
  background-color: var(--color-background-light);
  border-radius: var(--radius-xl);
  overflow: hidden;
  transition: all var(--transition-normal);
  position: relative;
  scroll-snap-align: start;
  border: 1px solid var(--color-border);
  display: flex;
  flex-direction: column;
}

@media (max-width: 767px) {
  .projects__card {
    min-width: 300px;
    max-width: 300px;
    height: auto;
    min-height: 350px;
  }
}

.projects__card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: var(--shadow-xl);
  border-color: var(--color-primary);
}

.projects__card:hover .projects__image {
  transform: scale(1.05);
}

.projects__card:hover .projects__overlay {
  opacity: 1;
}

.projects__image {
  width: 100%;
  height: 250px;
  background-size: cover;
  background-position: center;
  transition: transform var(--transition-slow);
  position: relative;
  flex-shrink: 0;
}

@media (max-width: 767px) {
  .projects__image {
    height: 200px;
  }
}

.projects__overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(49, 11, 238, 0.9), rgba(154, 144, 203, 0.8));
  opacity: 0;
  transition: opacity var(--transition-normal);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: var(--font-size-sm);
  text-transform: uppercase;
  letter-spacing: 1px;
}

.projects__content {
  padding: var(--spacing-lg);
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.projects__title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  margin: 0;
  color: var(--color-text-primary);
  line-height: 1.3;
}

.projects__description {
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  line-height: 1.5;
  margin: 0;
  flex: 1;
}

.projects__buttons {
  display: flex;
  gap: var(--spacing-sm);
  margin-top: auto;
}

.projects__btn {
  flex: 1;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-md);
  font-size: var(--font-size-xs);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all var(--transition-normal);
  cursor: pointer;
  border: none;
  text-decoration: none;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
}

.projects__btn--primary {
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-light));
  color: var(--color-text-primary);
}

.projects__btn--primary:hover {
  background: linear-gradient(135deg, var(--color-primary-hover), var(--color-primary));
  transform: translateY(-2px);
  box-shadow: 0 8px 20px -5px rgba(49, 11, 238, 0.4);
}

.projects__btn--secondary {
  background: transparent;
  border: 2px solid var(--color-border);
  color: var(--color-text-secondary);
}

.projects__btn--secondary:hover {
  border-color: var(--color-primary);
  color: var(--color-primary);
  background: rgba(49, 11, 238, 0.1);
  transform: translateY(-2px);
}

.projects__btn-icon {
  width: 16px;
  height: 16px;
  fill: currentColor;
}

@media (max-width: 767px) {
  .projects__buttons {
    flex-direction: column;
  }
  
  .projects__btn {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-xs);
  }
}

/* Projects navigation hint */
.projects__scroll-hint {
  text-align: center;
  color: var(--color-text-muted);
  font-size: var(--font-size-xs);
  margin-top: var(--spacing-sm);
  font-style: italic;
}

@media (max-width: 767px) {
  .projects__container {
    padding: var(--spacing-md);
    gap: var(--spacing-md);
  }
}

.form__group {
  margin-bottom: var(--spacing-lg);
}
.form__label {
  display: block;
  margin-bottom: var(--spacing-sm);
  font-weight: 600;
  color: var(--color-text-primary);
  font-size: var(--font-size-sm);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}
.form__input, .form__textarea {
  width: 100%;
  padding: var(--spacing-md) var(--spacing-lg);
  background-color: var(--color-secondary);
  border: 2px solid var(--color-border);
  border-radius: var(--radius-lg);
  color: var(--color-text-primary);
  font-size: var(--font-size-base);
  font-family: var(--font-primary);
  transition: all var(--transition-normal);
  box-sizing: border-box;
}
.form__input::-moz-placeholder, .form__textarea::-moz-placeholder {
  color: var(--color-text-muted);
  opacity: 0.8;
}
.form__input::placeholder, .form__textarea::placeholder {
  color: var(--color-text-muted);
  opacity: 0.8;
}
.form__input:focus, .form__textarea:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(49, 11, 238, 0.15);
  background-color: var(--color-background-light);
  transform: translateY(-1px);
}
.form__input:hover, .form__textarea:hover {
  border-color: var(--color-primary-light);
}
.form__input:invalid:not(:-moz-placeholder-shown), .form__textarea:invalid:not(:-moz-placeholder-shown) {
  border-color: var(--color-error);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}
.form__input:invalid:not(:placeholder-shown), .form__textarea:invalid:not(:placeholder-shown) {
  border-color: var(--color-error);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}
.form__input:valid:not(:-moz-placeholder-shown), .form__textarea:valid:not(:-moz-placeholder-shown) {
  border-color: var(--color-success);
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}
.form__input:valid:not(:placeholder-shown), .form__textarea:valid:not(:placeholder-shown) {
  border-color: var(--color-success);
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}
.form__textarea {
  min-height: 140px;
  resize: vertical;
  font-family: var(--font-primary);
  line-height: 1.6;
}
.form__error {
  color: var(--color-error);
  font-size: var(--font-size-sm);
  margin-top: var(--spacing-xs);
  display: none;
}
.form__error.show {
  display: block;
}
.form__success {
  color: var(--color-success);
  font-size: var(--font-size-sm);
  margin-top: var(--spacing-xs);
  display: none;
}
.form__success.show {
  display: block;
}

.contact-form {
  max-width: 750px;
  width: 100%;
  margin: 0 auto;
  padding: var(--spacing-3xl);
  background: linear-gradient(135deg, rgba(29, 24, 52, 0.95), rgba(19, 16, 35, 0.98));
  border-radius: var(--radius-xl);
  border: 1px solid rgba(49, 11, 238, 0.3);
  box-shadow: 
    0 25px 50px -12px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(49, 11, 238, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.contact-form::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, 
    transparent, 
    var(--color-primary), 
    var(--color-primary-light), 
    var(--color-primary), 
    transparent
  );
}

.contact-form::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: conic-gradient(
    from 0deg at 50% 50%,
    transparent 0deg,
    rgba(49, 11, 238, 0.05) 60deg,
    transparent 120deg,
    rgba(154, 144, 203, 0.05) 180deg,
    transparent 240deg,
    rgba(49, 11, 238, 0.05) 300deg,
    transparent 360deg
  );
  animation: rotate 20s linear infinite;
  pointer-events: none;
  z-index: -1;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.contact-form .form__group {
  position: relative;
  margin-bottom: var(--spacing-xl);
}

.contact-form .form__label {
  display: block;
  margin-bottom: var(--spacing-sm);
  font-weight: 600;
  color: var(--color-text-primary);
  font-size: var(--font-size-sm);
  text-transform: uppercase;
  letter-spacing: 1px;
  position: relative;
}

.contact-form .form__label::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 30px;
  height: 2px;
  background: linear-gradient(90deg, var(--color-primary), var(--color-primary-light));
  border-radius: var(--radius-full);
}

.contact-form .form__input,
.contact-form .form__textarea {
  width: 100%;
  padding: var(--spacing-lg) var(--spacing-xl);
  background: rgba(41, 34, 73, 0.6);
  border: 2px solid rgba(58, 49, 104, 0.5);
  border-radius: var(--radius-lg);
  color: var(--color-text-primary);
  font-size: var(--font-size-base);
  font-family: var(--font-primary);
  transition: all var(--transition-normal);
  box-sizing: border-box;
  backdrop-filter: blur(10px);
  position: relative;
}

.contact-form .form__input::placeholder,
.contact-form .form__textarea::placeholder {
  color: rgba(154, 144, 203, 0.7);
  font-style: italic;
}

.contact-form .form__input:focus,
.contact-form .form__textarea:focus {
  outline: none;
  border-color: var(--color-primary);
  background: rgba(41, 34, 73, 0.8);
  box-shadow: 
    0 0 0 3px rgba(49, 11, 238, 0.2),
    0 8px 25px -5px rgba(49, 11, 238, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

.contact-form .form__input:hover:not(:focus),
.contact-form .form__textarea:hover:not(:focus) {
  border-color: rgba(49, 11, 238, 0.7);
  background: rgba(41, 34, 73, 0.7);
}

.contact-form .form__textarea {
  min-height: 150px;
  resize: vertical;
  font-family: var(--font-primary);
  line-height: 1.6;
}

.contact-form .btn {
  width: 100%;
  margin-top: var(--spacing-lg);
  padding: var(--spacing-lg) var(--spacing-xl);
  font-weight: 700;
  font-size: var(--font-size-base);
  text-transform: uppercase;
  letter-spacing: 1px;
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-light));
  border: 2px solid transparent;
  border-radius: var(--radius-lg);
  color: var(--color-text-primary);
  cursor: pointer;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.contact-form .btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(255, 255, 255, 0.2), 
    transparent
  );
  transition: left var(--transition-slow);
}

.contact-form .btn:hover {
  transform: translateY(-3px);
  box-shadow: 
    0 15px 35px -5px rgba(49, 11, 238, 0.4),
    0 5px 15px -5px rgba(49, 11, 238, 0.2);
  background: linear-gradient(135deg, var(--color-primary-hover), var(--color-primary));
}

.contact-form .btn:hover::before {
  left: 100%;
}

.contact-form .btn:active {
  transform: translateY(-1px);
}

/* Enhanced form validation styles */
.contact-form .form__input.error,
.contact-form .form__textarea.error {
  border-color: var(--color-error);
  background: rgba(239, 68, 68, 0.1);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.2);
}

.contact-form .form__input.valid,
.contact-form .form__textarea.valid {
  border-color: var(--color-success);
  background: rgba(16, 185, 129, 0.1);
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.2);
}

.contact-form .form__error {
  color: var(--color-error);
  font-size: var(--font-size-sm);
  margin-top: var(--spacing-xs);
  display: none;
  font-weight: 500;
}

.contact-form .form__error.show {
  display: block;
  animation: slideDown 0.3s ease-out;
}

.contact-form .form__success {
  color: var(--color-success);
  font-size: var(--font-size-sm);
  margin-top: var(--spacing-xs);
  display: none;
  font-weight: 500;
}

.contact-form .form__success.show {
  display: block;
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Mobile responsive adjustments */
@media (max-width: 767px) {
  .contact-form {
    padding: var(--spacing-lg);
    margin: 0 var(--spacing-sm);
    max-width: calc(100vw - var(--spacing-lg));
    width: calc(100% - var(--spacing-lg));
    box-sizing: border-box;
  }
  
  .contact-form .form__input,
  .contact-form .form__textarea {
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: var(--font-size-sm);
    width: 100%;
    box-sizing: border-box;
  }
  
  .contact-form .btn {
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: var(--font-size-sm);
    width: 100%;
    box-sizing: border-box;
  }
  
  /* Prevent horizontal overflow */
  .hero__image-bg {
    background-size: cover;
    background-position: center;
  }
  
  /* Fix hero image overflow */
  .hero__image {
    width: 100%;
    max-width: 100%;
    overflow: hidden;
  }
  
  /* Ensure all images stay within bounds */
  .projects__image {
    width: 100%;
    max-width: 100%;
    background-size: cover;
    background-position: center;
  }
}

.mobile-menu {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background-color: rgba(19, 16, 35, 0.98);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  z-index: 1000;
  padding: var(--spacing-2xl);
}
.mobile-menu.active {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  animation: fadeIn var(--transition-normal) ease-in-out;
}
.mobile-menu__nav {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
  text-align: center;
}
.mobile-menu__link {
  font-size: var(--font-size-2xl);
  font-weight: 600;
  color: var(--color-text-primary);
  transition: all var(--transition-normal);
  padding: var(--spacing-md);
  border-radius: var(--radius-lg);
}
.mobile-menu__link:hover {
  color: var(--color-primary-light);
  background-color: rgba(49, 11, 238, 0.1);
  transform: scale(1.05);
}
.mobile-menu__close {
  position: absolute;
  top: var(--spacing-xl);
  right: var(--spacing-xl);
  background: none;
  border: none;
  color: var(--color-text-primary);
  font-size: var(--font-size-2xl);
  cursor: pointer;
  padding: var(--spacing-sm);
  border-radius: var(--radius-md);
  transition: all var(--transition-normal);
}
.mobile-menu__close:hover {
  background-color: var(--color-secondary);
  transform: rotate(90deg);
}

.hamburger {
  display: none;
  flex-direction: column;
  cursor: pointer;
  padding: var(--spacing-sm);
  border-radius: var(--radius-md);
  background: none;
  border: 2px solid transparent;
  transition: all var(--transition-normal);
  position: relative;
  z-index: 1001;
}
@media (max-width: 767px) {
  .hamburger {
    display: flex;
  }
}
.hamburger:hover {
  background-color: rgba(49, 11, 238, 0.1);
  border-color: var(--color-primary);
}
.hamburger:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}
.hamburger__line {
  width: 25px;
  height: 3px;
  background-color: var(--color-text-primary);
  margin: 2px 0;
  transition: all var(--transition-normal);
  border-radius: var(--radius-sm);
}
.hamburger.active {
  background-color: rgba(49, 11, 238, 0.15);
}
.hamburger.active .hamburger__line:nth-child(1) {
  transform: rotate(45deg) translate(6px, 6px);
}
.hamburger.active .hamburger__line:nth-child(2) {
  opacity: 0;
}
.hamburger.active .hamburger__line:nth-child(3) {
  transform: rotate(-45deg) translate(6px, -6px);
}

.mobile-menu {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background-color: rgba(19, 16, 35, 0.98);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  z-index: 1000;
  display: none;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: var(--spacing-xl);
  padding: var(--spacing-xl);
  box-sizing: border-box;
}
.mobile-menu.active {
  display: flex;
}
.mobile-menu__link {
  color: var(--color-text-primary);
  font-size: var(--font-size-xl);
  font-weight: 600;
  text-decoration: none;
  padding: var(--spacing-md);
  border-radius: var(--radius-lg);
  transition: all var(--transition-normal);
  text-align: center;
  width: 100%;
  max-width: 300px;
}
.mobile-menu__link:hover {
  color: var(--color-primary);
  background-color: rgba(49, 11, 238, 0.1);
  transform: translateY(-2px);
}
.mobile-menu__link:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

.scroll-to-top {
  position: fixed;
  bottom: var(--spacing-xl);
  right: var(--spacing-xl);
  width: 50px;
  height: 50px;
  background-color: var(--color-primary);
  color: var(--color-text-primary);
  border: none;
  border-radius: 50%;
  cursor: pointer;
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-normal);
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: center;
}
.scroll-to-top.visible {
  opacity: 1;
  visibility: visible;
}
.scroll-to-top:hover {
  background-color: var(--color-primary-hover);
  transform: translateY(-3px);
  box-shadow: var(--shadow-lg);
}
.scroll-to-top:active {
  transform: translateY(0);
}

.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: var(--color-text-primary);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.hidden {
  display: none !important;
}

.visible {
  display: block !important;
}

.fade-in {
  opacity: 0;
  animation: fadeIn var(--transition-normal) ease-in-out forwards;
}

.slide-up {
  opacity: 0;
  transform: translateY(30px);
  animation: slideUp var(--transition-normal) ease-out forwards;
}

.animate-on-scroll {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease-out;
}
.animate-on-scroll.in-view {
  opacity: 1;
  transform: translateY(0);
}

.social-links {
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
}
.social-links__link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  background-color: var(--color-secondary);
  border-radius: 50%;
  color: var(--color-text-primary);
  transition: all var(--transition-normal);
}
.social-links__link:hover {
  background-color: var(--color-primary);
  transform: translateY(-3px) scale(1.1);
  box-shadow: var(--shadow-lg);
}
.social-links__link svg {
  width: 20px;
  height: 20px;
}

.portfolio-footer {
  background-color: var(--color-background-light);
  border-top: 1px solid var(--color-border);
  margin-top: auto;
  padding: var(--spacing-2xl) var(--spacing-lg);
  position: relative;
}
.portfolio-footer::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--color-primary), transparent);
}

.footer__container {
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}
.footer__content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xl);
  text-align: center;
}
@media (min-width: 768px) {
  .footer__content {
    flex-direction: row;
    justify-content: space-between;
    text-align: left;
  }
}
.footer__nav {
  display: flex;
  gap: var(--spacing-lg);
  align-items: center;
}
@media (max-width: 767px) {
  .footer__nav {
    flex-direction: column;
    gap: var(--spacing-md);
  }
}
.footer__link {
  color: var(--color-text-secondary);
  text-decoration: none;
  font-weight: 500;
  font-size: var(--font-size-sm);
  transition: all var(--transition-normal);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-md);
}
.footer__link:hover {
  color: var(--color-primary-light);
  background-color: rgba(49, 11, 238, 0.1);
  transform: translateY(-1px);
}
.footer__link:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}
.footer__social {
  display: flex;
  gap: var(--spacing-md);
  align-items: center;
}
.footer__social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: var(--radius-full);
  background-color: var(--color-secondary);
  color: var(--color-text-secondary);
  transition: all var(--transition-normal);
  text-decoration: none;
}
.footer__social-link:hover {
  background-color: var(--color-primary);
  color: var(--color-text-primary);
  transform: translateY(-2px) scale(1.05);
  box-shadow: var(--shadow-md);
}
.footer__social-link:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}
.footer__social-icon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.footer__social-icon svg {
  width: 100%;
  height: 100%;
}
.footer__copyright {
  color: var(--color-text-muted);
  font-size: var(--font-size-sm);
  margin: 0;
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--color-border);
  width: 100%;
  text-align: center;
}
@media (min-width: 768px) {
  .footer__copyright {
    padding-top: var(--spacing-xl);
    margin-top: var(--spacing-xl);
  }
}

@media (max-width: 767px) {
  .portfolio-header {
    padding: var(--spacing-sm) var(--spacing-md);
  }
  .header__title {
    display: none;
  }
  .header__nav-container {
    gap: var(--spacing-sm);
  }
  .portfolio-nav {
    display: none;
  }
  
  /* Hero Section Mobile */
  .hero-section {
    padding: var(--spacing-lg) 0;
  }
  .hero__title {
    font-size: var(--font-size-2xl);
    line-height: 1.3;
  }
  .hero__subtitle {
    font-size: var(--font-size-base);
    line-height: 1.5;
  }
  
  /* Section Titles Mobile */
  .section-title {
    font-size: var(--font-size-2xl);
    margin: var(--spacing-2xl) 0 var(--spacing-lg) 0;
    padding: 0 var(--spacing-sm);
  }
  .section-subtitle {
    font-size: var(--font-size-xl);
    margin: var(--spacing-xl) 0 var(--spacing-md) 0;
    padding: 0 var(--spacing-sm);
  }
  .section-description {
    font-size: var(--font-size-base);
    padding: 0 var(--spacing-md);
    margin-bottom: var(--spacing-lg);
  }
  
  /* Skills Grid Mobile */
  .skills__grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
    padding: 0 var(--spacing-sm);
  }
  
  /* Projects Mobile */
  .projects__container {
    grid-template-columns: 1fr;
    padding: var(--spacing-md);
    gap: var(--spacing-md);
  }
  
  /* Buttons Mobile */
  .btn {
    width: 100%;
    justify-content: center;
  }
  .btn:not(.hamburger) {
    font-size: var(--font-size-sm);
    padding: var(--spacing-sm) var(--spacing-md);
  }
  
  /* Contact Form Mobile */
  .contact-form {
    padding: var(--spacing-lg);
    margin: 0 var(--spacing-md);
    max-width: calc(100% - var(--spacing-lg));
  }
  
  /* Layout Mobile */
  .main-content {
    padding: var(--spacing-sm) var(--spacing-sm);
  }
  .content-container {
    padding: 0;
  }
  
  /* Footer Mobile */
  .portfolio-footer {
    padding: var(--spacing-lg) var(--spacing-md);
  }
  
  /* Spacing adjustments */
  .hero-section,
  .skills-section,
  .projects-section,
  .about-section {
    margin: var(--spacing-lg) 0;
  }
}
@media (min-width: 768px) {
  .portfolio-header {
    padding: var(--spacing-md) var(--spacing-xl);
  }
  .header__title {
    display: block;
  }
  .header__nav-container {
    gap: var(--spacing-lg);
  }
  .hamburger {
    display: none;
  }
  .mobile-menu {
    display: none !important;
  }
  .portfolio-nav {
    display: flex;
  }
  .hero__title {
    font-size: var(--font-size-4xl);
  }
  .skills__grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }
  .projects__container {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
  }
  .contact-form {
    padding: var(--spacing-xl);
  }
  .portfolio-footer {
    padding: var(--spacing-2xl) var(--spacing-xl);
  }
}
@media (min-width: 1024px) {
  .portfolio-header {
    padding: var(--spacing-lg) var(--spacing-2xl);
  }
  .header__nav-container {
    gap: var(--spacing-xl);
  }
  .portfolio-nav {
    gap: var(--spacing-md);
  }
  .hero__title {
    font-size: var(--font-size-5xl);
  }
  .skills__grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }
  .projects__container {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-xl);
    padding: var(--spacing-xl) var(--spacing-lg);
  }
  .contact-form {
    padding: var(--spacing-2xl);
  }
  .portfolio-footer {
    padding: var(--spacing-3xl) var(--spacing-2xl);
  }
}
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
  html {
    scroll-behavior: auto;
  }
}
@media (prefers-contrast: high) {
  :root {
    --color-border: #ffffff;
    --color-text-secondary: #ffffff;
  }
}
@media print {
  .hamburger,
  .mobile-menu,
  .scroll-to-top,
  .btn {
    display: none !important;
  }
  body {
    background: white !important;
    color: black !important;
  }
  .hero__title,
  .skills__title,
  .projects__title {
    color: black !important;
  }
}/*# sourceMappingURL=style.css.map */